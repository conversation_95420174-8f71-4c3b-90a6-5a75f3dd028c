curl -X POST "https://comave-magento.ddev.site/rest/V1/cooling-off/orders/354/cancel" \
  -H "Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.eyJ1aWQiOjEzODA5LCJ1dHlwaWQiOjMsImlhdCI6MTc1MzY5NTc1MSwiZXhwIjoxNzUzNzMxNzUxfQ.r7q9LvksCj9SSqidmHNskf-OP1IfMn4Yhp54SXRB9SQ" \
  -H "Content-Type: application/json" \
  -d '{"reason": "Customer requested cancellation during cooling-off period"}'


  curl -X POST \
  -H "Content-Type: application/json" \
  -H "Store: en_store" \
  -H "Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.eyJ1aWQiOjEzODA5LCJ1dHlwaWQiOjMsImlhdCI6MTc1MzY5NTc1MSwiZXhwIjoxNzUzNzMxNzUxfQ.r7q9LvksCj9SSqidmHNskf-OP1IfMn4Yhp54SXRB9SQ" \
  -d '{
    "query": "query { products(filter: {}) { total_count items { id name sku } } }"
  }' \
  http://your-domain/graphql