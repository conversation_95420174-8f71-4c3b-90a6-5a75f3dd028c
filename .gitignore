.env
php.*.ini
/.ddev/homeadditions/.gitconfig
/.editorconfig
/.htaccess
/.php_cs.dist
/*.md
/index.php
/*.txt
/*.sample
!/bin
/bin/magento
/bin/.htaccess
!/.gitignore
!/.magento.app.yaml
!/.magento.env.yaml*
!/.magento
!/.magento/**
/.magento/local
!/app
!/app/*/
/app/etc/env.php
!/app/code/**
!/app/design/**
!/app/etc/config.php
!/app/i18n/**
!/auth.json
!/build_options.ini
!/composer.json
!/composer.lock
/dev
/generated
/lib
!/magento-vars.php
!/m2-hotfixes
!/m2-hotfixes/**
!/php.ini
/phpserver
!/update
!/update/.htaccess
!/README.md
!/op-exclude.txt
/pub/static
/pub/media/*
!/pub/media/theme/
!/pub/media/wysiwyg/pearl_theme
!/pub/media/wysiwyg/pearl_theme/**/*
!/pub/media/css_weltpixel
!/pub/media/css_weltpixel/**
!/pub/media/weltpixel
!/pub/media/weltpixel/**
!/pub/media/logo/
!/pub/media/logo/**
/setup
/var
!/var/.htaccess
*--dump.sql*
*--dump*.sql
.idea
*.swp
*.bak
*.orig
**/.DS_Store
