<?xml version="1.0"?>
<!--
/**
 * Copyright © Commercial Avenue
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Extension Attributes -->
    <type name="Magento\Sales\Api\Data\OrderInterface">
        <plugin name="add_cooling_off_extension_attributes"
                type="Comave\OrderCoolingOff\Plugin\Sales\AddCoolingOffExtensionAttributes"/>
    </type>

    <!-- Protect Cooling-Off Status - TEMPORARILY DISABLED FOR TESTING -->
    <!--
    <type name="Magento\Sales\Model\Order">
        <plugin name="protect_cooling_off_status"
                type="Comave\OrderCoolingOff\Plugin\Sales\ProtectCoolingOffStatus"
                sortOrder="10"/>
    </type>
    -->

    <!-- Order Repository Plugin -->
    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="add_cooling_off_data"
                type="Comave\OrderCoolingOff\Plugin\Sales\OrderRepositoryPlugin"/>
    </type>

    <!-- Order Cancellation Validation Plugin -->
    <type name="Magento\Sales\Model\Order">
        <plugin name="cooling_off_cancellation_validation"
                type="Comave\OrderCoolingOff\Plugin\Sales\OrderCancellationValidationPlugin"
                sortOrder="5"/>
    </type>

    <!-- Plugin to force online refunds for cooling-off cancellations -->
    <type name="Magento\Sales\Api\CreditmemoManagementInterface">
        <plugin name="cooling_off_force_online_refund"
                type="Comave\OrderCoolingOff\Plugin\Sales\CreditmemoManagementPlugin"
                sortOrder="5"/>
    </type>

    <!-- Plugin to force online refunds at Payment level for cooling-off cancellations -->
    <type name="Magento\Sales\Model\Order\Payment">
        <plugin name="cooling_off_payment_refund"
                type="Comave\OrderCoolingOff\Plugin\Sales\PaymentRefundPlugin"
                sortOrder="5"/>
    </type>

    <!-- Order Grid Data Provider -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <plugin name="add_cooling_off_grid_fields"
                type="Comave\OrderCoolingOff\Plugin\Ui\DataProvider\OrderGridDataProvider"/>
    </type>

    <!-- Marketplace Integration -->
    <type name="Webkul\Marketplace\Model\Orders">
        <plugin name="hide_cooling_off_orders"
                type="Comave\OrderCoolingOff\Plugin\Marketplace\HideCoolingOffOrders"/>
    </type>



    <!-- API Service Preferences -->
    <preference for="Comave\OrderCoolingOff\Api\CoolingOffManagementInterface"
                type="Comave\OrderCoolingOff\Model\CoolingOffManagement"/>

    <!-- Console Commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="cooling_off_add_attributes" xsi:type="object">Comave\OrderCoolingOff\Console\Command\AddOrderAttributes</item>
                <item name="cooling_off_monitor" xsi:type="object">Comave\OrderCoolingOff\Console\Command\MonitorCoolingOff</item>
            </argument>
        </arguments>
    </type>

</config>
