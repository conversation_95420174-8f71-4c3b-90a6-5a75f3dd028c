<?xml version="1.0"?>
<!--
/**
 * Copyright © Commercial Avenue
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    
    <!-- Apply cooling-off period when order is placed -->
    <event name="sales_order_place_after">
        <observer name="set_cooling_off_status"
                  instance="Comave\OrderCoolingOff\Observer\SetCoolingOffStatus"/>
    </event>

    <!-- Handle cooling-off logic after order cancellation -->
    <event name="order_cancel_after">
        <observer name="cooling_off_cancel_after"
                  instance="Comave\OrderCoolingOff\Observer\OrderCancelAfterObserver"/>
    </event>

    <!-- Also listen to sales_order_cancel_after in case core Magento uses different event -->
    <event name="sales_order_cancel_after">
        <observer name="cooling_off_sales_cancel_after"
                  instance="Comave\OrderCoolingOff\Observer\OrderCancelAfterObserver"/>
    </event>

</config>
