<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Comave\OrderCoolingOff\Service\ValidationService;
use Comave\Sales\Service\Order\CancellationService;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\CreditmemoFactory;
use Magento\Sales\Api\CreditmemoManagementInterface;
use Psr\Log\LoggerInterface;

/**
 * Observer to handle cooling-off specific logic after order cancellation
 */
class OrderCancelAfterObserver implements ObserverInterface
{
    /**
     * @param ValidationService $validationService
     * @param CancellationService $cancellationService
     * @param OrderRepositoryInterface $orderRepository
     * @param CreditmemoFactory $creditmemoFactory
     * @param CreditmemoManagementInterface $creditmemoManagement
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ValidationService $validationService,
        private readonly CancellationService $cancellationService,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly CreditmemoFactory $creditmemoFactory,
        private readonly CreditmemoManagementInterface $creditmemoManagement,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Handle order cancellation with cooling-off specific logic
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $this->logger->info('COOLING_OFF_OBSERVER: Observer triggered - START');

        /** @var Order $order */
        $order = $observer->getEvent()->getOrder();

        if (!$order instanceof Order) {
            $this->logger->info('COOLING_OFF_OBSERVER: Order is not instance of Order class - EXITING');
            return;
        }

        $this->logger->info(
            'COOLING_OFF_OBSERVER: Processing order cancellation with cooling-off logic',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'current_status' => $order->getStatus(),
                'current_state' => $order->getState(),
                'cooling_off_until' => $order->getCoolingOffUntil(),
                'observer_triggered' => true
            ]
        );

        try {
            // Check if this order was cancelled during cooling-off period
            if ($this->validationService->isInCoolingOffPeriod($order)) {
                $this->logger->info(
                    'COOLING_OFF_OBSERVER: Order was cancelled during cooling-off period - applying custom logic',
                    [
                        'order_id' => $order->getEntityId(),
                        'increment_id' => $order->getIncrementId(),
                        'is_in_cooling_off' => true
                    ]
                );

                // Allow this legitimate transition
                $this->validationService->allowTransition();

                // Set custom status for cooling-off cancellation
                $order->setStatus('canceled_by_customer');
                
                // Add comment explaining the cancellation
                $comment = __('Order cancelled by customer during cooling-off period via global cancellation flow.');
                $order->addCommentToStatusHistory(
                    (string) $comment,
                    'canceled_by_customer',
                    false, // not visible on front
                    true   // customer notified
                );

                // Save the order with new status
                $this->orderRepository->save($order);

                // For cooling-off cancellations, force immediate online refund if possible
                $this->processImmediateRefund($order);

                $this->logger->info(
                    'Successfully applied cooling-off cancellation logic',
                    [
                        'order_id' => $order->getEntityId(),
                        'new_status' => $order->getStatus()
                    ]
                );
            } else {
                $this->logger->info(
                    'COOLING_OFF_OBSERVER: Order was NOT in cooling-off period - standard cancellation completed',
                    [
                        'order_id' => $order->getEntityId(),
                        'increment_id' => $order->getIncrementId(),
                        'is_in_cooling_off' => false,
                        'cooling_off_until' => $order->getCoolingOffUntil()
                    ]
                );
            }

        } catch (\Exception $e) {
            $this->logger->error(
                'Error processing cooling-off cancellation logic',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );
            
            // Don't throw exception - let the cancellation complete even if our custom logic fails
        }
    }

    /**
     * Process immediate online refund for cooling-off cancellations
     *
     * @param Order $order
     * @return void
     */
    private function processImmediateRefund(Order $order): void
    {
        $this->logger->info(
            'COOLING_OFF_REFUND: Starting immediate refund process for cooling-off cancellation',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'has_invoices' => $order->hasInvoices(),
                'amount_paid' => $order->getPayment()->getAmountPaid(),
                'payment_method' => $order->getPayment()->getMethod(),
                'can_creditmemo' => $order->canCreditmemo(),
                'order_state' => $order->getState(),
                'order_status' => $order->getStatus()
            ]
        );

        try {
            // Check if order has invoices and payment was made
            if (!$order->hasInvoices() || $order->getPayment()->getAmountPaid() <= 0) {
                $this->logger->info(
                    'No refund needed - order has no invoices or no payment made',
                    ['order_id' => $order->getEntityId()]
                );
                return;
            }

            // Temporarily set order to processing state to allow refund
            $originalState = $order->getState();
            $originalStatus = $order->getStatus();

            if (!$order->canCreditmemo()) {
                $order->setState(Order::STATE_PROCESSING);
                $order->setStatus('processing');
            }

            if (!$order->canCreditmemo()) {
                $this->logger->warning(
                    'Order still cannot be refunded even after state change',
                    ['order_id' => $order->getEntityId()]
                );
                return;
            }

            // Get the last invoice
            $invoices = $order->getInvoiceCollection();
            $invoices->setOrder('created_at', 'DESC');
            $invoice = $invoices->getFirstItem();

            if (!$invoice || !$invoice->getEntityId()) {
                $this->logger->warning(
                    'No valid invoice found for refund',
                    ['order_id' => $order->getEntityId()]
                );
                return;
            }

            // Create credit memo
            $creditmemo = $this->creditmemoFactory->createByInvoice($invoice);
            if (!$creditmemo) {
                $this->logger->error(
                    'Failed to create credit memo',
                    ['order_id' => $order->getEntityId()]
                );
                return;
            }

            $creditmemo->addComment(
                __('Automatic online refund due to cooling-off period cancellation'),
                false,
                true
            );

            // Force online refund for cooling-off cancellations
            $creditmemo->setDoTransaction(true);

            $this->logger->info(
                'COOLING_OFF_REFUND: About to create immediate online refund',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'creditmemo_grand_total' => $creditmemo->getGrandTotal(),
                    'creditmemo_do_transaction' => $creditmemo->getDoTransaction(),
                    'payment_method' => $order->getPayment()->getMethod(),
                    'payment_can_refund' => $order->getPayment()->canRefund(),
                    'payment_method_instance_can_refund' => $order->getPayment()->getMethodInstance()->canRefund(),
                    'last_trans_id' => $order->getPayment()->getLastTransId(),
                    'amount_paid' => $order->getPayment()->getAmountPaid(),
                    'base_amount_paid_online' => $order->getPayment()->getBaseAmountPaidOnline(),
                    'forcing_online_refund' => true,
                    'offline_requested_parameter' => false
                ]
            );

            // Process online refund (second parameter false = online refund)
            $this->creditmemoManagement->refund($creditmemo, false);

            $this->logger->info(
                'COOLING_OFF_REFUND: Online refund call completed',
                [
                    'order_id' => $order->getEntityId(),
                    'creditmemo_id' => $creditmemo->getEntityId(),
                    'creditmemo_state' => $creditmemo->getState()
                ]
            );

            // Restore original state
            $order->setState($originalState);
            $order->setStatus($originalStatus);
            $this->orderRepository->save($order);

            $this->logger->info(
                'Immediate online refund completed successfully for cooling-off cancellation',
                [
                    'order_id' => $order->getEntityId(),
                    'creditmemo_id' => $creditmemo->getEntityId()
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to process immediate refund for cooling-off cancellation',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            // Fall back to the standard refund service
            $this->logger->info(
                'Falling back to standard refund service',
                ['order_id' => $order->getEntityId()]
            );

            try {
                $this->cancellationService->processRefundIfNeeded($order);
            } catch (\Exception $fallbackException) {
                $this->logger->error(
                    'Fallback refund also failed',
                    [
                        'order_id' => $order->getEntityId(),
                        'fallback_error' => $fallbackException->getMessage()
                    ]
                );
            }
        }
    }
}
