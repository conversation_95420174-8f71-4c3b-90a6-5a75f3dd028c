<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Sales\Api\Data\OrderInterface;

/**
 * Resolver for cooling_off_until field in CustomerOrder
 */
class CoolingOffUntil implements ResolverInterface
{
    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model']) || !($value['model'] instanceof OrderInterface)) {
            return null;
        }

        /** @var OrderInterface $order */
        $order = $value['model'];
        
        // Get cooling_off_until from extension attributes first
        $extensionAttributes = $order->getExtensionAttributes();
        if ($extensionAttributes && $extensionAttributes->getCoolingOffUntil()) {
            return $extensionAttributes->getCoolingOffUntil();
        }
        
        // Fallback to direct data access
        return $order->getData('cooling_off_until');
    }
}
