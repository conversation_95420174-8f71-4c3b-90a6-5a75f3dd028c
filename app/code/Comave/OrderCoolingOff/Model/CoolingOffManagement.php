<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Model;

use Comave\OrderCoolingOff\Api\CoolingOffManagementInterface;
/**
 * Cooling-off management implementation
 *
 * Note: Order cancellation is now handled through the global Magento cancellation flow
 * with cooling-off validation integrated via plugins/observers
 */
class CoolingOffManagement implements CoolingOffManagementInterface
{
    // No implementation needed - cancellation is now part of global order flow
}
