<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Plugin\Sales;

use Magento\Sales\Api\CreditmemoManagementInterface;
use Magento\Sales\Api\Data\CreditmemoInterface;
use Comave\OrderCoolingOff\Service\ValidationService;
use Psr\Log\LoggerInterface;

/**
 * Plugin to force online refunds for cooling-off period cancellations
 */
class CreditmemoManagementPlugin
{
    private ValidationService $validationService;
    private LoggerInterface $logger;

    public function __construct(
        ValidationService $validationService,
        LoggerInterface $logger
    ) {
        $this->validationService = $validationService;
        $this->logger = $logger;
    }

    /**
     * Force online refunds for cooling-off cancellations
     *
     * @param CreditmemoManagementInterface $subject
     * @param CreditmemoInterface $creditmemo
     * @param bool $offlineRequested
     * @return array
     */
    public function beforeRefund(
        CreditmemoManagementInterface $subject,
        CreditmemoInterface $creditmemo,
        $offlineRequested = false
    ): array {
        $order = $creditmemo->getOrder();
        
        $this->logger->info(
            'COOLING_OFF_CREDITMEMO_PLUGIN: Intercepting refund call',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'original_offline_requested' => $offlineRequested,
                'creditmemo_id' => $creditmemo->getEntityId(),
                'creditmemo_grand_total' => $creditmemo->getGrandTotal(),
                'order_status' => $order->getStatus(),
                'order_state' => $order->getState()
            ]
        );

        // Check if this order was cancelled during cooling-off period
        if ($this->validationService->isInCoolingOffPeriod($order)) {
            $this->logger->info(
                'COOLING_OFF_CREDITMEMO_PLUGIN: Order is in cooling-off period - forcing online refund',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'original_offline_requested' => $offlineRequested,
                    'forcing_online_refund' => true
                ]
            );

            // Force online refund by setting offlineRequested to false
            $offlineRequested = false;
            
            // Also ensure the creditmemo is set to do transaction
            $creditmemo->setDoTransaction(true);
            
            $this->logger->info(
                'COOLING_OFF_CREDITMEMO_PLUGIN: Forced online refund parameters',
                [
                    'order_id' => $order->getEntityId(),
                    'offline_requested_final' => $offlineRequested,
                    'creditmemo_do_transaction' => $creditmemo->getDoTransaction()
                ]
            );
        } else {
            $this->logger->info(
                'COOLING_OFF_CREDITMEMO_PLUGIN: Order is NOT in cooling-off period - using original refund settings',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'offline_requested' => $offlineRequested,
                    'cooling_off_until' => $order->getCoolingOffUntil()
                ]
            );
        }

        return [$creditmemo, $offlineRequested];
    }
}
