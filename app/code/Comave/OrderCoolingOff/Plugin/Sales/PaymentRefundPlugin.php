<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Plugin\Sales;

use Magento\Sales\Model\Order\Payment;
use Magento\Sales\Api\OrderRepositoryInterface;
use Comave\OrderCoolingOff\Service\ValidationService;
use Psr\Log\LoggerInterface;

/**
 * Plugin to force online refunds for cooling-off period cancellations
 */
class PaymentRefundPlugin
{
    private ValidationService $validationService;
    private LoggerInterface $logger;
    private OrderRepositoryInterface $orderRepository;

    public function __construct(
        ValidationService $validationService,
        LoggerInterface $logger,
        OrderRepositoryInterface $orderRepository
    ) {
        $this->validationService = $validationService;
        $this->logger = $logger;
        $this->orderRepository = $orderRepository;
    }

    /**
     * Force online refunds for cooling-off cancellations
     *
     * @param Payment $subject
     * @param $creditmemo
     * @return array
     */
    public function beforeRefund(
        Payment $subject,
        $creditmemo
    ): array {
        $order = $subject->getOrder();
        
        $this->logger->info(
            'COOLING_OFF_PAYMENT_PLUGIN: Intercepting payment refund call',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'creditmemo_id' => $creditmemo->getEntityId(),
                'creditmemo_grand_total' => $creditmemo->getGrandTotal(),
                'creditmemo_do_transaction_before' => $creditmemo->getDoTransaction(),
                'order_status' => $order->getStatus(),
                'order_state' => $order->getState(),
                'payment_method' => $subject->getMethod()
            ]
        );

        // Check if this order was cancelled during cooling-off period
        if ($this->validationService->isInCoolingOffPeriod($order)) {
            $this->logger->info(
                'COOLING_OFF_PAYMENT_PLUGIN: Order is in cooling-off period - forcing online refund',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'original_do_transaction' => $creditmemo->getDoTransaction(),
                    'forcing_online_refund' => true
                ]
            );

            // Force online refund by setting doTransaction to true
            $creditmemo->setDoTransaction(true);

            $this->logger->info(
                'COOLING_OFF_PAYMENT_PLUGIN: Forced online refund parameters',
                [
                    'order_id' => $order->getEntityId(),
                    'creditmemo_do_transaction_after' => $creditmemo->getDoTransaction()
                ]
            );
        } else {
            $this->logger->info(
                'COOLING_OFF_PAYMENT_PLUGIN: Order is NOT in cooling-off period - using original refund settings',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'creditmemo_do_transaction' => $creditmemo->getDoTransaction(),
                    'cooling_off_until' => $order->getCoolingOffUntil()
                ]
            );
        }

        return [$creditmemo];
    }

    /**
     * Add cooling-off cancellation comment after refund is processed
     *
     * @param Payment $subject
     * @param Payment $result
     * @param $creditmemo
     * @return Payment
     */
    public function afterRefund(
        Payment $subject,
        Payment $result,
        $creditmemo
    ): Payment {
        $order = $subject->getOrder();

        // Check if this order was cancelled during cooling-off period
        if ($this->validationService->isInCoolingOffPeriod($order)) {
            $this->logger->info(
                'COOLING_OFF_PAYMENT_PLUGIN: Adding cancellation comment after refund',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'current_status' => $order->getStatus()
                ]
            );

            try {
                // Set custom status for cooling-off cancellation
                $order->setStatus('canceled_by_customer');

                // Add comment explaining the cancellation
                $comment = __('Order cancelled by customer during cooling-off period.');
                $order->addCommentToStatusHistory(
                    (string) $comment,
                    'canceled_by_customer',
                    false, // not visible on front
                    false  // customer not notified (already notified by cancellation)
                );

                // Save the order with new status and comment
                $this->orderRepository->save($order);

                $this->logger->info(
                    'COOLING_OFF_PAYMENT_PLUGIN: Successfully added cancellation comment',
                    [
                        'order_id' => $order->getEntityId(),
                        'new_status' => $order->getStatus(),
                        'comment_added' => true
                    ]
                );
            } catch (\Exception $e) {
                $this->logger->error(
                    'COOLING_OFF_PAYMENT_PLUGIN: Failed to add cancellation comment',
                    [
                        'order_id' => $order->getEntityId(),
                        'error' => $e->getMessage()
                    ]
                );
            }
        }

        return $result;
    }
}
