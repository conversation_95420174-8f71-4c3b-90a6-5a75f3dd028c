<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Plugin\Sales;

use Magento\Sales\Model\Order;
use Comave\OrderCoolingOff\Service\ValidationService;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Plugin to validate cooling-off constraints before order cancellation
 */
class OrderCancellationValidationPlugin
{
    /**
     * @param ValidationService $validationService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ValidationService $validationService,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Validate cooling-off constraints before order cancellation
     *
     * @param Order $subject
     * @return array
     * @throws LocalizedException
     */
    public function beforeCancel(Order $subject): array
    {
        $this->logger->info(
            'Validating cooling-off constraints before order cancellation',
            [
                'order_id' => $subject->getEntityId(),
                'current_status' => $subject->getStatus(),
                'cooling_off_until' => $subject->getCoolingOffUntil()
            ]
        );

        // Check if order is in cooling-off period
        if ($this->validationService->isInCoolingOffPeriod($subject)) {
            $this->logger->info(
                'Order is in cooling-off period - cancellation allowed',
                ['order_id' => $subject->getEntityId()]
            );
        } else {
            $this->logger->info(
                'Order is not in cooling-off period - standard cancellation rules apply',
                ['order_id' => $subject->getEntityId()]
            );
        }

        // Always allow cancellation to proceed - we'll handle custom logic in observer
        return [];
    }
}
