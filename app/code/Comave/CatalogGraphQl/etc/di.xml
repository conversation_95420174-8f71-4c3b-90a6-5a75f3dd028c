<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products">
        <plugin name="apply_status_filter_plugin" type="Comave\CatalogGraphQl\Plugin\FilterEnabledProducts" disabled="true" />
        <plugin name="debug_products_resolver" type="Comave\CatalogGraphQl\Plugin\DebugProductsResolver" sortOrder="1" />
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Category\Products">
        <plugin name="removedisabled"
                type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                sortOrder="1" disabled="true"/>
    </type>

    <!-- Debug product search data provider -->
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\ProductSearch">
        <plugin name="debug_product_search" type="Comave\CatalogGraphQl\Plugin\DebugProductSearch" sortOrder="1" disabled="true" />
    </type>
</config>
