<?php
namespace Comave\CatalogGraphQl\Plugin;

use Magento\CatalogGraphQl\Model\Resolver\Products;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Psr\Log\LoggerInterface;
use Magento\Store\Model\StoreManagerInterface;

class DebugProductsResolver
{
    private LoggerInterface $logger;
    private StoreManagerInterface $storeManager;

    public function __construct(
        LoggerInterface $logger,
        StoreManagerInterface $storeManager
    ) {
        $this->logger = $logger;
        $this->storeManager = $storeManager;
    }

    public function aroundResolve(
        Products $subject,
        callable $proceed,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        // Log initial context
        $store = $context->getExtensionAttributes()->getStore();
        $this->logger->info('=== GraphQL Products Query Debug ===');
        $this->logger->info('Original Store ID: ' . ($store ? $store->getId() : 'NULL'));
        $this->logger->info('Store Code: ' . ($store ? $store->getCode() : 'NULL'));
        $this->logger->info('Website ID: ' . ($store ? $store->getWebsiteId() : 'NULL'));
        $this->logger->info('Args: ' . json_encode($args));

        // FORCE STORE ID TO 0 (admin store)
        $adminStore = $this->storeManager->getStore(0);
        $this->storeManager->setCurrentStore(0);
        $this->logger->info('FORCED Store ID to: ' . $this->storeManager->getStore()->getId());

        $result = $proceed($field, $context, $info, $value, $args);

        // Log result
        $this->logger->info('Result total_count: ' . ($result['total_count'] ?? 'NULL'));
        $this->logger->info('Result items count: ' . count($result['items'] ?? []));
        
        if (!empty($result['items'])) {
            $firstItem = $result['items'][0];
            $this->logger->info('First item ID: ' . ($firstItem['id'] ?? 'NULL'));
            $this->logger->info('First item SKU: ' . ($firstItem['sku'] ?? 'NULL'));
        }

        $this->logger->info('=== End GraphQL Products Query Debug ===');

        return $result;
    }
}
