<?php
namespace Comave\CatalogGraphQl\Plugin;

use Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\ProductSearch;
use Magento\Framework\Api\SearchCriteriaInterface;
use Psr\Log\LoggerInterface;
use Magento\Store\Model\StoreManagerInterface;

class DebugProductSearch
{
    private LoggerInterface $logger;
    private StoreManagerInterface $storeManager;

    public function __construct(
        LoggerInterface $logger,
        StoreManagerInterface $storeManager
    ) {
        $this->logger = $logger;
        $this->storeManager = $storeManager;
    }

    public function aroundGetList(
        ProductSearch $subject,
        callable $proceed,
        SearchCriteriaInterface $searchCriteria,
        array $attributeNames,
        bool $isChildSearch = false,
        bool $keepSearchResults = false
    ) {
        $this->logger->info('=== ProductSearch::getList Debug ===');
        $this->logger->info('Store ID: ' . $this->storeManager->getStore()->getId());
        $this->logger->info('Website ID: ' . $this->storeManager->getStore()->getWebsiteId());
        $this->logger->info('Search Criteria: ' . json_encode([
            'current_page' => $searchCriteria->getCurrentPage(),
            'page_size' => $searchCriteria->getPageSize(),
            'filter_groups_count' => count($searchCriteria->getFilterGroups()),
        ]));

        // Log filter groups
        foreach ($searchCriteria->getFilterGroups() as $i => $filterGroup) {
            foreach ($filterGroup->getFilters() as $j => $filter) {
                $this->logger->info("Filter [$i][$j]: {$filter->getField()} {$filter->getConditionType()} {$filter->getValue()}");
            }
        }

        $result = $proceed($searchCriteria, $attributeNames, $isChildSearch, $keepSearchResults);

        $this->logger->info('ProductSearch Result:');
        $this->logger->info('Total Count: ' . $result->getTotalCount());
        $this->logger->info('Items Count: ' . count($result->getItems()));
        
        if ($result->getItems()) {
            $firstItem = current($result->getItems());
            $this->logger->info('First Item ID: ' . $firstItem->getId());
            $this->logger->info('First Item SKU: ' . $firstItem->getSku());
        }

        $this->logger->info('=== End ProductSearch::getList Debug ===');

        return $result;
    }
}
