<?php
namespace Comave\CatalogGraphQl\Plugin;

use Magento\CatalogGraphQl\Model\Resolver\Products;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Catalog\Model\Product\Attribute\Source\Status as ProductStatus;

class FilterEnabledProducts
{
    public function aroundResolve(
        Products $subject,
        callable $proceed,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $result = $proceed($field, $context, $info, $value, $args);

        if (!isset($args['filter']['status']['eq'])) {
            return $result;
        }

        $statusFilter = (int) $args['filter']['status']['eq'];

        // Filter only enabled products
        if ($statusFilter === ProductStatus::STATUS_ENABLED) {
            $filteredItems = array_filter($result['items'], function ($product) {
                return isset($product['status']) && (int) $product['status'] === ProductStatus::STATUS_ENABLED;
            });

            $result['items'] = array_values($filteredItems);
            $result['total_count'] = count($result['items']); // Fix: Update total_count after filtering
        }

        return $result;
    }
}
