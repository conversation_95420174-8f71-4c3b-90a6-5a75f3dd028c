<?php

declare(strict_types=1);

namespace Comave\Sales\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\DeploymentConfig;

/**
 * Observer to add frontend_base_url variable to email templates
 */
class EmailTemplateVarsObserver implements ObserverInterface
{
    /**
     * Configuration key for frontend base URL
     */
    private const FRONTEND_BASE_URL_CONFIG_KEY = 'frontend_base_url';

    /**
     * @param DeploymentConfig $deploymentConfig
     */
    public function __construct(
        private readonly DeploymentConfig $deploymentConfig
    ) {
    }

    /**
     * Add frontend_base_url from env.php to email template variables
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $transport = $observer->getEvent()->getTransport();

        if ($transport) {
            $frontendBaseUrl = $this->deploymentConfig->get(self::FRONTEND_BASE_URL_CONFIG_KEY);

            if ($frontendBaseUrl) {
                // Check if transport is an object with setData method or an array
                if (is_object($transport) && method_exists($transport, 'setData')) {
                    $transport->setData(self::FRONTEND_BASE_URL_CONFIG_KEY, rtrim($frontendBaseUrl, '/'));
                } elseif (is_array($transport)) {
                    // For array-based transports, we can't modify them directly
                    // This would need to be handled differently depending on the specific event
                }
            }
        }
    }
}
