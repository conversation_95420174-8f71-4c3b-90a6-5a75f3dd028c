<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Service\Order;

use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Status\HistoryFactory;
use Magento\Sales\Api\OrderManagementInterface;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Comave\Sales\Service\Order\RefundService;
use Comave\Sales\Service\Order\NotificationService;
use Psr\Log\LoggerInterface;

/**
 * Order Cancellation Service
 */
class CancellationService
{
    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param HistoryFactory $historyFactory
     * @param OrderManagementInterface $orderManagement
     * @param EventManagerInterface $eventManager
     * @param RefundService $refundService
     * @param NotificationService $notificationService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly HistoryFactory $historyFactory,
        private readonly OrderManagementInterface $orderManagement,
        private readonly EventManagerInterface $eventManager,
        private readonly RefundService $refundService,
        private readonly NotificationService $notificationService,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Cancel order with specific reason
     *
     * @param Order $order
     * @param string $reason
     * @param string $comment
     * @return void
     * @throws LocalizedException
     */
    public function cancelOrderWithReason(Order $order, string $reason, string $comment = ''): void
    {
        $this->logger->info(
            'Starting order cancellation process',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'current_status' => $order->getStatus(),
                'current_state' => $order->getState(),
                'reason' => $reason,
                'comment' => $comment,
                'can_cancel' => $order->canCancel(),
                'has_invoices' => $order->hasInvoices(),
                'can_creditmemo' => $order->canCreditmemo()
            ]
        );

        try {
            // if (!$order->canCancel()) {
            //     $this->logger->warning(
            //         'Order cancellation failed - order cannot be cancelled',
            //         [
            //             'order_id' => $order->getEntityId(),
            //             'current_status' => $order->getStatus(),
            //             'current_state' => $order->getState()
            //         ]
            //     );
            //     throw new LocalizedException(__('Order cannot be cancelled.'));
            // }

            $this->orderManagement->cancel($order->getEntityId());

            $order->setStatus($reason);
            
            $this->addCancellationComment($order, $reason, $comment);

            $this->orderRepository->save($order);

            $this->processRefundIfNeeded($order);

            $this->notificationService->sendCancellationNotification($order, $reason, $comment);

            $this->eventManager->dispatch(
                'comave_order_cancelled_with_reason',
                [
                    'order' => $order,
                    'reason' => $reason,
                    'comment' => $comment
                ]
            );

            $this->logger->info(
                'Order cancelled successfully',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'reason' => $reason
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to cancel order',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage()
                ]
            );
            throw new LocalizedException(__('Failed to cancel order: %1', $e->getMessage()));
        }
    }

    /**
     * Add cancellation comment to order history
     *
     * @param Order $order
     * @param string $reason
     * @param string $comment
     * @return void
     */
    private function addCancellationComment(Order $order, string $reason, string $comment): void
    {
        $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
        
        $historyComment = $reasonLabel;
        if (!empty($comment)) {
            $historyComment .= ': ' . $comment;
        }

        $history = $this->historyFactory->create();
        $history->setParentId($order->getEntityId())
            ->setStatus($reason)
            ->setComment($historyComment)
            ->setIsCustomerNotified(true)
            ->setIsVisibleOnFront(true);

        $order->addStatusHistory($history);
    }

    /**
     * Process refund if payment was captured
     *
     * @param Order $order
     * @return void
     */
    public function processRefundIfNeeded(Order $order): void
    {
        $this->logger->info(
            'Checking if refund is needed for cancelled order',
            [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'current_state' => $order->getState(),
                'current_status' => $order->getStatus(),
                'has_invoices' => $order->hasInvoices(),
                'can_creditmemo' => $order->canCreditmemo(),
                'invoice_count' => $order->getInvoiceCollection()->getSize(),
                'payment_method' => $order->getPayment()->getMethod(),
                'amount_paid' => $order->getPayment()->getAmountPaid(),
                'base_amount_paid_online' => $order->getPayment()->getBaseAmountPaidOnline()
            ]
        );

        try {
            // Check if order has invoices and payment was made
            if ($order->hasInvoices() && $order->getPayment()->getAmountPaid() > 0) {

                // If order is in canceled state but has invoices, we need to temporarily
                // change state to processing to allow refund, then restore canceled state
                $originalState = $order->getState();
                $originalStatus = $order->getStatus();
                $needsStateRestore = false;

                if (!$order->canCreditmemo()) {
                    $this->logger->info(
                        'Order cannot be refunded in current state - temporarily changing to processing',
                        [
                            'order_id' => $order->getEntityId(),
                            'original_state' => $originalState,
                            'original_status' => $originalStatus
                        ]
                    );

                    // Temporarily set to processing state to allow refund
                    $order->setState(Order::STATE_PROCESSING);
                    $order->setStatus('processing');
                    $needsStateRestore = true;
                }

                if ($order->canCreditmemo()) {
                    $this->logger->info(
                        'Conditions met for automatic refund - calling refund service',
                        ['order_id' => $order->getEntityId()]
                    );

                    $this->refundService->processAutomaticRefund($order);

                    $this->logger->info(
                        'Refund processing completed',
                        ['order_id' => $order->getEntityId()]
                    );
                } else {
                    $this->logger->warning(
                        'Still cannot refund order even after state change',
                        ['order_id' => $order->getEntityId()]
                    );
                }

                // Restore original state if we changed it
                if ($needsStateRestore) {
                    $this->logger->info(
                        'Restoring original order state after refund',
                        [
                            'order_id' => $order->getEntityId(),
                            'restoring_state' => $originalState,
                            'restoring_status' => $originalStatus
                        ]
                    );

                    $order->setState($originalState);
                    $order->setStatus($originalStatus);
                }

            } else {
                $this->logger->info(
                    'Refund conditions not met - skipping automatic refund',
                    [
                        'order_id' => $order->getEntityId(),
                        'has_invoices' => $order->hasInvoices(),
                        'amount_paid' => $order->getPayment()->getAmountPaid(),
                        'can_creditmemo' => $order->canCreditmemo()
                    ]
                );
            }
        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to process automatic refund for cancelled order',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );
        }
    }
}
